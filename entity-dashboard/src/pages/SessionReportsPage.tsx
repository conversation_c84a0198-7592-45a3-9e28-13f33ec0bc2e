import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  Fab,
} from '@mui/material';
import {
  Assessment,
  Schedule,
  CheckCircle,
  Cancel,
  FileDownload,
  PictureAsPdf,
  Add,
  People,
  TrendingUp,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import ApiService from '../services/ApiService';

interface SessionBasic {
  id: number;
  name: string;
  startTime: string;
  endTime?: string;
  attendeeCount?: number;
  totalSubscribers?: number;
}

interface SessionAttendance {
  sessionId: number;
  sessionName: string;
  attendeeCount: number;
  absenteeCount: number;
  attendanceRate: number;
}

const SessionReportsPage: React.FC = () => {
  const navigate = useNavigate();
  const [sessions, setSessions] = useState<SessionBasic[]>([]);
  const [selectedSessionId, setSelectedSessionId] = useState<string>('');
  const [sessionAttendance, setSessionAttendance] = useState<SessionAttendance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSessions();
    fetchSessionAttendanceOverview();
  }, []);

  const fetchSessions = async () => {
    try {
      const response = await ApiService.get<SessionBasic[]>('/api/sessions');
      setSessions(response.data || []);
    } catch (error) {
      console.error('Error fetching sessions:', error);
      setError('Failed to fetch sessions');
    }
  };

  const fetchSessionAttendanceOverview = async () => {
    try {
      setIsLoading(true);
      const response = await ApiService.get<SessionAttendance[]>('/api/reports/sessions/overview');
      setSessionAttendance(response.data || []);
    } catch (error) {
      console.error('Error fetching session attendance overview:', error);
      setError('Failed to fetch session attendance data');
    } finally {
      setIsLoading(false);
    }
  };

  const generateSessionPdf = async (sessionId?: string) => {
    const targetSessionId = sessionId || selectedSessionId;
    if (!targetSessionId) return;

    try {
      setIsGeneratingReport(true);
      const response = await ApiService.get(`/api/reports/sessions/${targetSessionId}/attendance-pdf`, {
        responseType: 'blob',
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `session_${targetSessionId}_attendance_report.pdf`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to generate session PDF:', error);
      setError('Failed to generate PDF report');
    } finally {
      setIsGeneratingReport(false);
    }
  };

  const exportSessionData = () => {
    const csvContent = [
      ['Session Name', 'Attendees', 'Absentees', 'Attendance Rate'].join(','),
      ...sessionAttendance.map(session => [
        `"${session.sessionName}"`,
        session.attendeeCount,
        session.absenteeCount,
        `${session.attendanceRate.toFixed(1)}%`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `session_reports_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatDateTime = (isoString: string) => {
    return new Date(isoString).toLocaleString();
  };

  const getAttendanceColor = (rate: number) => {
    if (rate >= 80) return 'success';
    if (rate >= 60) return 'warning';
    return 'error';
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Session Reports
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Analyze attendance patterns and generate session-specific reports
          </Typography>
        </Box>
        <Button
          variant="outlined"
          onClick={() => navigate('/dashboard/reports')}
          sx={{ ml: 2 }}
        >
          Back to Reports
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Quick Report Generation */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <PictureAsPdf />
                </Avatar>
                <Box>
                  <Typography variant="h6" fontWeight="bold">
                    Generate Report
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Create detailed PDF reports
                  </Typography>
                </Box>
              </Box>

              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>Select Session</InputLabel>
                <Select
                  value={selectedSessionId}
                  label="Select Session"
                  onChange={(e) => setSelectedSessionId(e.target.value)}
                >
                  {sessions.map((session) => (
                    <MenuItem key={session.id} value={session.id}>
                      {session.name} - {formatDateTime(session.startTime)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Button
                fullWidth
                variant="contained"
                onClick={() => generateSessionPdf()}
                disabled={!selectedSessionId || isGeneratingReport}
                startIcon={isGeneratingReport ? <CircularProgress size={20} /> : <PictureAsPdf />}
              >
                {isGeneratingReport ? 'Generating...' : 'Generate PDF Report'}
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Session Attendance Overview */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Avatar sx={{ bgcolor: 'success.main' }}>
                    <TrendingUp />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="bold">
                      Session Attendance Overview
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Attendance rates across all sessions
                    </Typography>
                  </Box>
                </Box>
                <Tooltip title="Export to CSV">
                  <IconButton onClick={exportSessionData} color="primary">
                    <FileDownload />
                  </IconButton>
                </Tooltip>
              </Box>

              {sessionAttendance.length > 0 ? (
                <TableContainer component={Paper} variant="outlined">
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Session</TableCell>
                        <TableCell align="center">Attendees</TableCell>
                        <TableCell align="center">Absentees</TableCell>
                        <TableCell align="center">Attendance Rate</TableCell>
                        <TableCell align="center">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {sessionAttendance.map((session) => (
                        <TableRow key={session.sessionId}>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {session.sessionName}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              icon={<CheckCircle />}
                              label={session.attendeeCount}
                              color="success"
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              icon={<Cancel />}
                              label={session.absenteeCount}
                              color="error"
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              label={`${session.attendanceRate.toFixed(1)}%`}
                              color={getAttendanceColor(session.attendanceRate)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell align="center">
                            <Tooltip title="Generate PDF Report">
                              <IconButton
                                onClick={() => generateSessionPdf(session.sessionId.toString())}
                                color="primary"
                                size="small"
                              >
                                <PictureAsPdf />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <Alert severity="info">
                  No session data available
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="create session"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => navigate('/dashboard/sessions')}
      >
        <Add />
      </Fab>
    </Box>
  );
};

export default SessionReportsPage;
